import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Button from '../../components/bootstrap/Button';
import Icon from '../../components/icon/Icon';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const OperationDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample production data
	const productionLines = [
		{
			id: 1,
			name: 'Milk Processing Line 1',
			status: 'Running',
			efficiency: 92,
			currentProduct: 'Whole Milk',
			dailyTarget: 5000,
			currentOutput: 4600,
		},
		{
			id: 2,
			name: 'Yogurt Production Line',
			status: 'Running',
			efficiency: 88,
			currentProduct: 'Greek Yogurt',
			dailyTarget: 2000,
			currentOutput: 1760,
		},
		{
			id: 3,
			name: 'Cheese Processing Line',
			status: 'Maintenance',
			efficiency: 0,
			currentProduct: 'Cheddar Cheese',
			dailyTarget: 800,
			currentOutput: 0,
		},
		{
			id: 4,
			name: 'Packaging Line A',
			status: 'Running',
			efficiency: 95,
			currentProduct: 'Milk Bottles',
			dailyTarget: 10000,
			currentOutput: 9500,
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Running': return 'success';
			case 'Maintenance': return 'warning';
			case 'Stopped': return 'danger';
			case 'Idle': return 'info';
			default: return 'secondary';
		}
	};

	const getEfficiencyColor = (efficiency: number) => {
		if (efficiency >= 90) return 'success';
		if (efficiency >= 75) return 'warning';
		return 'danger';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Operations Dashboard - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='Factory' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Operations Dashboard</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='PlayArrow'
						color='success'
						isLight
						className='me-2'>
						Start Production
					</Button>
					<Button
						icon='Settings'
						color='primary'
						isLight>
						Settings
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* KPI Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Speed' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>91.7%</div>
										<div className='text-muted'>Overall Efficiency</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											+3.2% from yesterday
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Inventory' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>15,860</div>
										<div className='text-muted'>Units Produced Today</div>
										<div className='small text-info'>
											<Icon icon='Schedule' className='me-1' />
											Target: 17,800
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Build' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>1</div>
										<div className='text-muted'>Lines Under Maintenance</div>
										<div className='small text-warning'>
											<Icon icon='Schedule' className='me-1' />
											Est. 2 hours remaining
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='CheckCircle' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold h4 mb-0'>99.2%</div>
										<div className='text-muted'>Quality Score</div>
										<div className='small text-success'>
											<Icon icon='TrendingUp' className='me-1' />
											Above target
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Production Lines Status */}
				<div className='row mb-4'>
					<div className='col-12'>
						<Card stretch>
							<CardHeader>
								<CardTitle tag='div' className='h5'>
									<Icon icon='Factory' /> Production Lines Status
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern table-hover'>
										<thead>
											<tr>
												<th>Production Line</th>
												<th>Status</th>
												<th>Current Product</th>
												<th>Efficiency</th>
												<th>Daily Progress</th>
												<th>Output</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{productionLines.map((line) => (
												<tr key={line.id}>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-shrink-0'>
																<div className='ratio ratio-1x1 me-3' style={{ width: 48 }}>
																	<div className='bg-l10-primary rounded-2 d-flex align-items-center justify-content-center'>
																		<Icon icon='Factory' size='lg' color='primary' />
																	</div>
																</div>
															</div>
															<div className='flex-grow-1'>
																<div className='fw-bold'>{line.name}</div>
																<div className='text-muted small'>Line ID: {line.id}</div>
															</div>
														</div>
													</td>
													<td>
														<Badge color={getStatusColor(line.status)} isLight>
															{line.status}
														</Badge>
													</td>
													<td>{line.currentProduct}</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={line.efficiency}
																	color={getEfficiencyColor(line.efficiency)}
																	height={8}
																/>
															</div>
															<small className='text-muted fw-bold'>
																{line.efficiency}%
															</small>
														</div>
													</td>
													<td>
														<div className='d-flex align-items-center'>
															<div className='flex-grow-1 me-2'>
																<Progress
																	value={(line.currentOutput / line.dailyTarget) * 100}
																	color='info'
																	height={8}
																/>
															</div>
															<small className='text-muted'>
																{Math.round((line.currentOutput / line.dailyTarget) * 100)}%
															</small>
														</div>
														<div className='small text-muted mt-1'>
															{line.currentOutput.toLocaleString()} / {line.dailyTarget.toLocaleString()}
														</div>
													</td>
													<td>
														<div className='fw-bold'>{line.currentOutput.toLocaleString()}</div>
														<div className='text-muted small'>units</div>
													</td>
													<td>
														<Button
															color='primary'
															isLight
															size='sm'
															icon='Visibility'
															className='me-2'>
															Monitor
														</Button>
														<Button
															color='warning'
															isLight
															size='sm'
															icon='Settings'>
															Control
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Quick Actions */}
				<div className='row'>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Production Control</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='row g-3'>
									<div className='col-6'>
										<Button
											color='primary'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/operations/production'>
											<Icon icon='Factory' className='me-2' />
											Production
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='success'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/operations/quality'>
											<Icon icon='VerifiedUser' className='me-2' />
											Quality Control
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='warning'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/operations/maintenance'>
											<Icon icon='Build' className='me-2' />
											Maintenance
										</Button>
									</div>
									<div className='col-6'>
										<Button
											color='info'
											isLight
											className='w-100'
											size='lg'
											tag='a'
											to='/operations/scheduling'>
											<Icon icon='Schedule' className='me-2' />
											Scheduling
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-lg-6'>
						<Card stretch>
							<CardHeader>
								<CardTitle>Recent Alerts</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='timeline'>
									<div className='timeline-item'>
										<div className='timeline-marker bg-warning'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Maintenance scheduled</h6>
											<p className='timeline-text'>Cheese Processing Line maintenance started</p>
											<small className='text-muted'>30 minutes ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-success'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Target achieved</h6>
											<p className='timeline-text'>Packaging Line A exceeded daily target</p>
											<small className='text-muted'>2 hours ago</small>
										</div>
									</div>
									<div className='timeline-item'>
										<div className='timeline-marker bg-info'></div>
										<div className='timeline-content'>
											<h6 className='timeline-title'>Quality check passed</h6>
											<p className='timeline-text'>Batch #2024-001 approved for packaging</p>
											<small className='text-muted'>4 hours ago</small>
										</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default OperationDashboard;
