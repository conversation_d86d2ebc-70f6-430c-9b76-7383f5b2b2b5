import React from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import Head from 'next/head';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../layout/SubHeader/SubHeader';
import Page from '../../layout/Page/Page';
import Card, { CardBody, CardHeader, CardTitle } from '../../components/bootstrap/Card';
import Icon from '../../components/icon/Icon';
import Button from '../../components/bootstrap/Button';
import Badge from '../../components/bootstrap/Badge';
import Progress from '../../components/bootstrap/Progress';

const SalesMarketingDashboard: NextPage = () => {
	const { t } = useTranslation(['common', 'menu']);

	// Sample sales data
	const salesData = [
		{
			id: 'SM-001',
			campaign: 'Summer Dairy Special',
			status: 'Active',
			budget: '₹50,000',
			spent: '₹32,000',
			leads: 245,
			conversions: 67,
			roi: 185,
			startDate: '2024-01-01',
			endDate: '2024-01-31',
		},
		{
			id: 'SM-002',
			campaign: 'Organic Milk Launch',
			status: 'Active',
			budget: '₹75,000',
			spent: '₹45,000',
			leads: 189,
			conversions: 52,
			roi: 142,
			startDate: '2024-01-15',
			endDate: '2024-02-15',
		},
		{
			id: 'SM-003',
			campaign: 'Festival Offers',
			status: 'Completed',
			budget: '₹30,000',
			spent: '₹28,500',
			leads: 156,
			conversions: 89,
			roi: 298,
			startDate: '2023-12-15',
			endDate: '2024-01-05',
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Active':
				return 'success';
			case 'Completed':
				return 'info';
			case 'Paused':
				return 'warning';
			case 'Cancelled':
				return 'danger';
			default:
				return 'secondary';
		}
	};

	const getRoiColor = (roi: number) => {
		if (roi >= 200) return 'success';
		if (roi >= 150) return 'warning';
		return 'danger';
	};

	return (
		<PageWrapper>
			<Head>
				<title>Sales & Marketing Dashboard - Aadhan Dairy ERP</title>
			</Head>
			<SubHeader>
				<SubHeaderLeft>
					<Icon icon='TrendingUp' size='2x' color='primary' />
					<span className='h4 mb-0 fw-bold ms-3'>Sales & Marketing Dashboard</span>
				</SubHeaderLeft>
				<SubHeaderRight>
					<Button
						icon='Add'
						color='primary'
						isLight
						className='me-2'>
						New Campaign
					</Button>
					<Button
						icon='Analytics'
						color='info'
						isLight>
						View Analytics
					</Button>
				</SubHeaderRight>
			</SubHeader>
			<Page>
				{/* Sales Overview Cards */}
				<div className='row mb-4'>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-success'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='TrendingUp' size='3x' color='success' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold fs-3 mb-0'>₹2.5L</div>
										<div className='text-muted'>Monthly Revenue</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-info'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='People' size='3x' color='info' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold fs-3 mb-0'>590</div>
										<div className='text-muted'>Total Leads</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-warning'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='ShoppingCart' size='3x' color='warning' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold fs-3 mb-0'>208</div>
										<div className='text-muted'>Conversions</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
					<div className='col-xl-3 col-md-6'>
						<Card className='bg-l10-primary'>
							<CardBody>
								<div className='d-flex align-items-center'>
									<div className='flex-shrink-0'>
										<Icon icon='Campaign' size='3x' color='primary' />
									</div>
									<div className='flex-grow-1 ms-3'>
										<div className='fw-bold fs-3 mb-0'>5</div>
										<div className='text-muted'>Active Campaigns</div>
									</div>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>

				{/* Campaigns List */}
				<div className='row'>
					<div className='col-12'>
						<Card>
							<CardHeader>
								<CardTitle tag='h4'>
									<Icon icon='Campaign' className='me-2' />
									Marketing Campaigns
								</CardTitle>
							</CardHeader>
							<CardBody>
								<div className='table-responsive'>
									<table className='table table-modern'>
										<thead>
											<tr>
												<th>Campaign ID</th>
												<th>Campaign Name</th>
												<th>Status</th>
												<th>Budget</th>
												<th>Spent</th>
												<th>Leads</th>
												<th>Conversions</th>
												<th>ROI</th>
												<th>Actions</th>
											</tr>
										</thead>
										<tbody>
											{salesData.map((campaign) => (
												<tr key={campaign.id}>
													<td>
														<strong>{campaign.id}</strong>
													</td>
													<td>{campaign.campaign}</td>
													<td>
														<Badge
															color={getStatusColor(campaign.status)}
															isLight>
															{campaign.status}
														</Badge>
													</td>
													<td>{campaign.budget}</td>
													<td>{campaign.spent}</td>
													<td>{campaign.leads}</td>
													<td>{campaign.conversions}</td>
													<td>
														<Badge
															color={getRoiColor(campaign.roi)}
															isLight>
															{campaign.roi}%
														</Badge>
													</td>
													<td>
														<Button
															color='primary'
															size='sm'
															isLight
															icon='Visibility'
															className='me-1'>
															View
														</Button>
														<Button
															color='secondary'
															size='sm'
															isLight
															icon='Edit'>
															Edit
														</Button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common', 'menu'])),
	},
});

export default SalesMarketingDashboard;
