import React, { useEffect } from 'react';
import type { NextPage } from 'next';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import Head from 'next/head';
import PageWrapper from '../../layout/PageWrapper/PageWrapper';
import Page from '../../layout/Page/Page';
import Card, { CardBody } from '../../components/bootstrap/Card';
import Icon from '../../components/icon/Icon';
import Button from '../../components/bootstrap/Button';

const LogoutPage: NextPage = () => {
	const { t } = useTranslation(['common']);
	const router = useRouter();

	useEffect(() => {
		// Clear any authentication tokens/session data here
		// For now, we'll just redirect after a short delay
		const timer = setTimeout(() => {
			router.push('/auth-pages/login');
		}, 3000);

		return () => clearTimeout(timer);
	}, [router]);

	const handleLoginRedirect = () => {
		router.push('/auth-pages/login');
	};

	return (
		<PageWrapper>
			<Head>
				<title>Logout - Aadhan Dairy ERP</title>
			</Head>
			<Page>
				<div className='row h-100 align-items-center justify-content-center'>
					<div className='col-xl-4 col-lg-6 col-md-8 shadow-3d-container'>
						<Card className='shadow-3d-dark' data-tour='login-page'>
							<CardBody>
								<div className='text-center my-5'>
									<Icon
										icon='ExitToApp'
										size='4x'
										color='success'
										className='mb-4'
									/>
									<h2 className='fw-bold mb-3'>Successfully Logged Out</h2>
									<p className='text-muted mb-4'>
										You have been successfully logged out of Aadhan Dairy ERP.
										Thank you for using our system!
									</p>
									<p className='text-muted small mb-4'>
										You will be redirected to the login page in a few seconds...
									</p>
									<Button
										color='primary'
										size='lg'
										onClick={handleLoginRedirect}
										icon='Login'>
										Back to Login
									</Button>
								</div>
							</CardBody>
						</Card>
					</div>
				</div>
			</Page>
		</PageWrapper>
	);
};

export const getStaticProps: GetStaticProps = async ({ locale }) => ({
	props: {
		// @ts-ignore
		...(await serverSideTranslations(locale, ['common'])),
	},
});

export default LogoutPage;
